import { describe, expect, test } from "bun:test"
import { Session } from "../../src/session/index"

describe("fileRegex", () => {
  const template = `This is a @valid/path/to/a/file and it should also match at
the beginning of a line:

@another-valid/path/to/a/file

but this is not:

   - Adds a "Co-authored-by:" footer which clarifies which AI agent
     helped create this commit, using an appropriate \`noreply@...\`
     or \`<EMAIL>\` email address.

We also need to deal with files followed by @commas, ones
with @file-extensions.md, even @multiple.extensions.bak,
hidden directorys like @.config/ or files like @.bashrc
and ones at the end of a sentence like @foo.md.

Also shouldn't forget @/absolute/paths.txt with and @/without/extensions,
as well as @~/home-files and @~/paths/under/home.txt.

If the reference is \`@quoted/in/backticks\` then it shouldn't match at all.`

  const matches = Array.from(template.matchAll(Session.fileRegex))

  test("should extract exactly 12 file references", () => {
    expect(matches.length).toBe(12)
  })

  test("should extract valid/path/to/a/file", () => {
    expect(matches[0][1]).toBe("valid/path/to/a/file")
  })

  test("should extract another-valid/path/to/a/file", () => {
    expect(matches[1][1]).toBe("another-valid/path/to/a/file")
  })

  test("should extract paths ignoring comma after", () => {
    expect(matches[2][1]).toBe("commas")
  })

  test("should extract a path with a file extension and comma after", () => {
    expect(matches[3][1]).toBe("file-extensions.md")
  })

  test("should extract a path with multiple dots and comma after", () => {
    expect(matches[4][1]).toBe("multiple.extensions.bak")
  })

  test("should extract hidden directory", () => {
    expect(matches[5][1]).toBe(".config/")
  })

  test("should extract hidden file", () => {
    expect(matches[6][1]).toBe(".bashrc")
  })

  test("should extract a file ignoring period at end of sentence", () => {
    expect(matches[7][1]).toBe("foo.md")
  })

  test("should extract an absolute path with an extension", () => {
    expect(matches[8][1]).toBe("/absolute/paths.txt")
  })

  test("should extract an absolute path without an extension", () => {
    expect(matches[9][1]).toBe("/without/extensions")
  })

  test("should extract an absolute path in home directory", () => {
    expect(matches[10][1]).toBe("~/home-files")
  })

  test("should extract an absolute path under home directory", () => {
    expect(matches[11][1]).toBe("~/paths/under/home.txt")
  })

  test("should not match when preceded by backtick", () => {
    const backtickTest = "This `@should/not/match` should be ignored"
    const backtickMatches = Array.from(backtickTest.matchAll(Session.fileRegex))
    expect(backtickMatches.length).toBe(0)
  })

  test("should not match email addresses", () => {
    const emailTest = "Contact <EMAIL> for help"
    const emailMatches = Array.from(emailTest.matchAll(Session.fileRegex))
    expect(emailMatches.length).toBe(0)
  })
})