# Base URL: https://www.saucedemo.com/

Feature: Shopping Cart Functionality

  Scenario: Add a product to the cart and verify cart icon updates
    Given the user is on the Login Page
    When the user logs in with username "standard_user" and password "secret_sauce"
    And the user navigates to the Inventory Page
    And the cart icon is initially empty
    When the user clicks the "Add to cart" button for a product
    Then the cart icon should update to reflect "1" item