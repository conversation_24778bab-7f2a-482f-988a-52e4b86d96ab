import { test, expect } from "@playwright/test"

test("Error message is displayed for invalid login", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.fill('[data-test="username"]', "invalid_user")
  await page.fill('[data-test="password"]', "invalid_pass")
  await page.click('[data-test="login-button"]')
  await expect(page.locator('[data-test="error"]')).toBeVisible()
  await expect(page.locator('[data-test="error"]')).toContainText("Username and password do not match")
})
