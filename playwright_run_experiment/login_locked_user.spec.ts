import { test, expect } from "@playwright/test"

test("Error message is displayed for locked account login", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.fill('[data-test="username"]', "locked_out_user")
  await page.fill('[data-test="password"]', "secret_sauce")
  await page.click('[data-test="login-button"]')
  await expect(page.locator('[data-test="error"]')).toBeVisible()
  await expect(page.locator('[data-test="error"]')).toContainText("Sorry, this user has been locked out.")
})
