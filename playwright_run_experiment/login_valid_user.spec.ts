import { test, expect } from "@playwright/test"

test("Valid user can log in and is redirected to Inventory Page", async ({ page }) => {
  await page.goto("https://www.saucedemo.com/")
  await page.fill('[data-test="username"]', "standard_user")
  await page.fill('[data-test="password"]', "secret_sauce")
  await page.click('[data-test="login-button"]')
  await expect(page).toHaveURL(/inventory.html/)
  await expect(page.locator(".inventory_list")).toBeVisible()
})
