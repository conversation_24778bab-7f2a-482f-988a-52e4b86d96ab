import { test, expect } from "@playwright/test"

const BASE_URL = "https://www.saucedemo.com/"
const USERNAME = "standard_user"
const PASSWORD = "secret_sauce"

test("Add a product to the cart and verify cart icon updates", async ({ page }) => {
  // Step 1: Go to Login Page
  await page.goto(BASE_URL)
  await expect(page).toHaveURL(BASE_URL)

  // Step 2: Log in
  await page.fill('input[data-test="username"]', USERNAME)
  await page.fill('input[data-test="password"]', PASSWORD)
  await page.click('input[data-test="login-button"]')
  await expect(page).toHaveURL(/inventory/)

  // Step 3: Ensure on Inventory Page
  await expect(page.locator(".inventory_list")).toBeVisible()

  // Step 4: Cart icon is initially empty
  const cartBadge = page.locator(".shopping_cart_badge")
  await expect(cartBadge).toHaveCount(0)

  // Step 5: Click "Add to cart" for the first product
  const addToCartButton = page.locator('button[data-test^="add-to-cart"]').first()
  await addToCartButton.click()

  // Step 6: Cart icon should update to reflect "1" item
  await expect(cartBadge).toHaveText("1")
})
