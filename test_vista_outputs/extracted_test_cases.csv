Requirement name,Test case name,Preconditions,Test steps
Verify that users can log in using a valid username and password and are granted access to the inventory page.,Verify that user is authenticated and redirected to the Inventory Page upon successful login with valid credentials,"• User is on the Login Page
• Valid username and password are available for testing","1. Enter a valid username in the Username input field.
2. Enter the corresponding valid password in the Password input field.
3. Click the 'Login' button."
Verify that an error message is displayed when a user attempts to log in with invalid credentials or a locked account.,Verify that an error message is displayed when logging in with an invalid username and password,"• The user is on the Login Page of the Swag Labs web application.
• The entered username and password do not match any valid credentials in the system.","1. Navigate to the Login Page.
2. Enter an invalid username in the Username input field.
3. Enter an invalid password in the Password input field.
4. Click the 'Login' button."
Verify that an error message is displayed when a user attempts to log in with invalid credentials or a locked account.,Verify that an error message is displayed when logging in with a locked account,"• The user is on the Login Page of the Swag Labs web application.
• The entered username corresponds to a locked account in the system.
• The entered password is correct for the locked account.","1. Navigate to the Login Page.
2. Enter the username of a locked account in the Username input field.
3. Enter the correct password for the locked account in the Password input field.
4. Click the 'Login' button."
Verify that an error message is displayed when a user attempts to log in with invalid credentials or a locked account.,Verify that the error message area is not visible before any login attempt,"• The user is on the Login Page of the Swag Labs web application.
• No login attempt has been made yet.",1. Navigate to the Login Page.
Verify that an error message is displayed when a user attempts to log in with invalid credentials or a locked account.,Verify that the error message does not reveal sensitive system information when login fails,"• The user is on the Login Page of the Swag Labs web application.
• The entered username and password do not match any valid credentials in the system.","1. Navigate to the Login Page.
2. Enter an invalid username in the Username input field.
3. Enter an invalid password in the Password input field.
4. Click the 'Login' button."
Verify that an error message is displayed when a user attempts to log in with invalid credentials or a locked account.,Verify that the error message area is accessible in all supported browsers when a login error occurs,"• The user is on the Login Page of the Swag Labs web application using a supported browser.
• The entered username and password do not match any valid credentials in the system.","1. Navigate to the Login Page using a supported browser.
2. Enter an invalid username in the Username input field.
3. Enter an invalid password in the Password input field.
4. Click the 'Login' button."
"Verify that the inventory page displays a list of products, each with an image, descriptive text, and price.","Verify that the Inventory Page displays a list of products, each with an image, descriptive text, and price","• The user account is valid and active.
• The user has successfully authenticated and is on the Inventory Page.","1. Log in to the Swag Labs web application using valid credentials.
2. Navigate to the Inventory Page.
3. Observe the list of products displayed on the Inventory Page.
4. For each product entry, check for the presence of a product image.
5. For each product entry, check for the presence of descriptive text (such as product name or description).
6. For each product entry, check for the presence of the product's price."
"Verify that the inventory page displays a list of products, each with an image, descriptive text, and price.",Verify that all product information is visible and clearly associated with the correct product entry on the Inventory Page,"• The user account is valid and active.
• The user has successfully authenticated and is on the Inventory Page.","1. Log in to the Swag Labs web application using valid credentials.
2. Navigate to the Inventory Page.
3. For each product entry, verify that the image, descriptive text, and price are visually grouped together and not mixed with other product entries."
"Verify that the inventory page displays a list of products, each with an image, descriptive text, and price.",Verify that the Inventory Page displays the product list in all supported browsers,"• The user account is valid and active.
• The user has successfully authenticated and is on the Inventory Page.
• The test environment includes Chrome, Firefox, Edge, and Safari browsers.","1. Log in to the Swag Labs web application using valid credentials in Chrome.
2. Navigate to the Inventory Page and observe the product list.
3. Repeat steps 1-2 in Firefox.
4. Repeat steps 1-2 in Edge.
5. Repeat steps 1-2 in Safari."
Verify that each product has an 'Add to cart' button that adds the product to the cart when clicked.,Verify that each product entry displays an 'Add to cart' button on the Inventory Page,"• User is authenticated and is on the Inventory Page.
• The Inventory Page displays a list of products with their respective details.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Locate each product entry in the product list.
4. Check for the presence of an 'Add to cart' button for each product entry."
Verify that each product has an 'Add to cart' button that adds the product to the cart when clicked.,Verify that clicking the 'Add to cart' button adds the product to the cart and updates the cart icon,"• User is authenticated and is on the Inventory Page.
• The Inventory Page displays a list of products with their respective details.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Identify a product entry and click its 'Add to cart' button.
4. Observe the cart icon in the navigation bar."
Verify that each product has an 'Add to cart' button that adds the product to the cart when clicked.,Verify that the 'Add to cart' button is accessible via keyboard for each product entry,"• User is authenticated and is on the Inventory Page.
• The Inventory Page displays a list of products with their respective details.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Use the Tab key to navigate to the 'Add to cart' button for a product entry.
4. Press Enter or Space to activate the 'Add to cart' button.
5. Observe the cart icon in the navigation bar."
Verify that each product has an 'Add to cart' button that adds the product to the cart when clicked.,Verify that the 'Add to cart' button is present and functional in all supported browsers,"• User is authenticated and is on the Inventory Page.
• The Inventory Page displays a list of products with their respective details.
• Supported browsers (Chrome, Firefox, Edge, Safari) are available for testing.","1. Log in with valid credentials on the Login Page using Chrome.
2. Navigate to the Inventory Page.
3. Locate a product entry and click its 'Add to cart' button.
4. Observe the cart icon in the navigation bar.
5. Repeat steps 1-4 in Firefox, Edge, and Safari."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon displays '0' when no items are in the cart,"• The user is authenticated and is viewing the Inventory Page with the cart icon visible.
• The cart is empty (no items have been added).","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon increments by one when a product is added to the cart,"• The user is authenticated and is viewing the Inventory Page with the cart icon visible.
• The cart is empty before the action.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for a product.
4. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon increments correctly when multiple products are added to the cart,"• The user is authenticated and is viewing the Inventory Page with the cart icon visible.
• The cart is empty before the actions.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for the first product.
4. Click the 'Add to cart' button for a second product.
5. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon decrements by one when a product is removed from the cart,"• The user is authenticated and is viewing the Cart Page with the cart icon visible.
• The cart contains one product before the removal action.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for a product.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Remove' button for the product in the cart.
6. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon decrements correctly when one of multiple products is removed from the cart,"• The user is authenticated and is viewing the Cart Page with the cart icon visible.
• The cart contains two products before the removal action.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for the first product.
4. Click the 'Add to cart' button for the second product.
5. Click the cart icon to go to the Cart Page.
6. Click the 'Remove' button for one product in the cart.
7. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon updates immediately after add or remove actions on any page with the cart icon visible,"• The user is authenticated and is viewing pages with the cart icon visible (Inventory Page and Cart Page).
• The cart is empty before the actions.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for a product.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Remove' button for the product in the cart.
6. Observe the cart icon in the navigation bar after each action."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon count never becomes negative after removing all items,"• The user is authenticated and is viewing the Cart Page with the cart icon visible.
• The cart contains one product before the removal action.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for a product.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Remove' button for the product in the cart.
6. Observe the cart icon in the navigation bar."
Verify that the cart icon updates to reflect the number of items added to the cart.,Verify that the cart icon displays the correct count after adding and removing multiple products in sequence,"• The user is authenticated and is viewing the Cart Page with the cart icon visible.
• The cart contains two products before the removal actions.","1. Log in with valid credentials on the Login Page.
2. Navigate to the Inventory Page.
3. Click the 'Add to cart' button for the first product.
4. Click the 'Add to cart' button for the second product.
5. Click the cart icon to go to the Cart Page.
6. Click the 'Remove' button for the first product.
7. Click the 'Remove' button for the second product.
8. Observe the cart icon in the navigation bar after each removal."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that clicking the cart icon displays the Cart Page with an up-to-date list of products in the cart,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• At least one product has been added to the cart","1. Log in with valid credentials on the Login Page.
2. Add one or more products to the cart from the Inventory Page by clicking the 'Add to cart' button for each desired product.
3. Click the cart icon in the navigation bar."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that the Cart Page displays an empty list when no products have been added to the cart,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• No products have been added to the cart","1. Log in with valid credentials on the Login Page.
2. Ensure no products are added to the cart on the Inventory Page.
3. Click the cart icon in the navigation bar."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that the Cart Page updates to reflect product removals before display,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• At least two products have been added to the cart, and one has been removed before viewing the Cart Page","1. Log in with valid credentials on the Login Page.
2. Add two or more products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Remove one product from the cart using the remove option on the Inventory Page or Cart Page.
4. Click the cart icon in the navigation bar."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that each product in the cart list on the Cart Page is clearly identified,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• At least one product has been added to the cart","1. Log in with valid credentials on the Login Page.
2. Add one or more products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that the Cart Page provides an option to remove each product from the cart,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• At least one product has been added to the cart","1. Log in with valid credentials on the Login Page.
2. Add one or more products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar."
Verify that clicking the cart icon displays the cart page with a list of added products.,Verify that the Cart Page provides an option to proceed to checkout,"• User is authenticated and on the Inventory Page
• Cart icon is visible in the navigation bar
• At least one product has been added to the cart","1. Log in with valid credentials on the Login Page.
2. Add one or more products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar."
Verify that users can remove items from the cart and the cart updates accordingly.,Verify that the selected product is removed from the cart when the remove option is clicked,"• User is authenticated
• User is on the Cart Page
• There is at least one product in the cart","1. Log in with valid credentials on the Login Page.
2. Add at least one product to the cart from the Inventory Page by clicking the 'Add to cart' button for a product.
3. Click the cart icon in the navigation bar to navigate to the Cart Page.
4. Click the remove option associated with the product you wish to remove."
Verify that users can remove items from the cart and the cart updates accordingly.,Verify that the cart icon updates to reflect the new item count after a product is removed from the cart,"• User is authenticated
• User is on the Cart Page
• There are at least two products in the cart","1. Log in with valid credentials on the Login Page.
2. Add two products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar to navigate to the Cart Page.
4. Click the remove option associated with one of the products in the cart."
Verify that users can remove items from the cart and the cart updates accordingly.,Verify that the removal of a product from the cart does not affect other products in the cart,"• User is authenticated
• User is on the Cart Page
• There are at least three products in the cart","1. Log in with valid credentials on the Login Page.
2. Add three products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar to navigate to the Cart Page.
4. Click the remove option associated with one of the products in the cart."
Verify that users can remove items from the cart and the cart updates accordingly.,Verify that the remove option is present for every product in the cart on the Cart Page,"• User is authenticated
• User is on the Cart Page
• There are multiple products in the cart","1. Log in with valid credentials on the Login Page.
2. Add multiple products to the cart from the Inventory Page by clicking the 'Add to cart' button for each product.
3. Click the cart icon in the navigation bar to navigate to the Cart Page."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.","Verify that the checkout form prompts for first name, last name, and ZIP/postal code fields","• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Observe the checkout form displayed."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.",Verify that the user cannot proceed to the next step of checkout if any required field is left blank,"• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Leave one or more of the 'First Name', 'Last Name', or 'ZIP/Postal Code' fields blank.
4. Click the 'Continue' button."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.",Verify that the user can proceed to the next step of checkout when all required fields are completed,"• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Enter a valid value in the 'First Name' field.
4. Enter a valid value in the 'Last Name' field.
5. Enter a valid value in the 'ZIP/Postal Code' field.
6. Click the 'Continue' button."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.",Verify that an error message is displayed when the 'First Name' field is left blank and 'Continue' is clicked,"• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Leave the 'First Name' field blank.
4. Enter valid values in the 'Last Name' and 'ZIP/Postal Code' fields.
5. Click the 'Continue' button."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.",Verify that an error message is displayed when the 'Last Name' field is left blank and 'Continue' is clicked,"• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Enter a valid value in the 'First Name' field.
4. Leave the 'Last Name' field blank.
5. Enter a valid value in the 'ZIP/Postal Code' field.
6. Click the 'Continue' button."
"Verify that the checkout process prompts the user for first name, last name, and ZIP/postal code.",Verify that an error message is displayed when the 'ZIP/Postal Code' field is left blank and 'Continue' is clicked,"• User is authenticated and logged in to the Swag Labs web application.
• User is on the Cart Page.
• There is at least one product in the cart.","1. Click the cart icon in the navigation bar to navigate to the Cart Page.
2. Click the 'Checkout' button on the Cart Page.
3. Enter a valid value in the 'First Name' field.
4. Enter a valid value in the 'Last Name' field.
5. Leave the 'ZIP/Postal Code' field blank.
6. Click the 'Continue' button."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the order summary displays an accurate list of products and their details after clicking 'Continue' during checkout,"• User is authenticated with valid credentials
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Log in with valid credentials on the Login Page.
2. Add one or more products to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to go to the Cart Page.
4. Click the 'Checkout' button on the Cart Page.
5. Enter valid values for First Name, Last Name, and ZIP/postal code in the checkout form.
6. Click the 'Continue' button.
7. Observe the order summary displayed on the order summary page."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the 'Finish' button is present and functional on the order summary page,"• User is authenticated with valid credentials
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Log in with valid credentials on the Login Page.
2. Add at least one product to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to go to the Cart Page.
4. Click the 'Checkout' button on the Cart Page.
5. Enter valid values for First Name, Last Name, and ZIP/postal code in the checkout form.
6. Click the 'Continue' button.
7. Observe the presence of the 'Finish' button on the order summary page.
8. Click the 'Finish' button."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the 'Cancel' button is present and functional on the order summary page,"• User is authenticated with valid credentials
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Log in with valid credentials on the Login Page.
2. Add at least one product to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to go to the Cart Page.
4. Click the 'Checkout' button on the Cart Page.
5. Enter valid values for First Name, Last Name, and ZIP/postal code in the checkout form.
6. Click the 'Continue' button.
7. Observe the presence of the 'Cancel' button on the order summary page.
8. Click the 'Cancel' button."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the order summary and action buttons are visible and accessible in Chrome browser,"• User is authenticated with valid credentials in Chrome browser
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Open the Swag Labs web application in Chrome browser.
2. Log in with valid credentials.
3. Add at least one product to the cart from the Inventory Page.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Checkout' button.
6. Enter valid checkout information and click 'Continue'.
7. Observe the order summary, 'Finish', and 'Cancel' buttons."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the order summary and action buttons are visible and accessible in Firefox browser,"• User is authenticated with valid credentials in Firefox browser
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Open the Swag Labs web application in Firefox browser.
2. Log in with valid credentials.
3. Add at least one product to the cart from the Inventory Page.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Checkout' button.
6. Enter valid checkout information and click 'Continue'.
7. Observe the order summary, 'Finish', and 'Cancel' buttons."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the order summary and action buttons are visible and accessible in Edge browser,"• User is authenticated with valid credentials in Edge browser
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Open the Swag Labs web application in Edge browser.
2. Log in with valid credentials.
3. Add at least one product to the cart from the Inventory Page.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Checkout' button.
6. Enter valid checkout information and click 'Continue'.
7. Observe the order summary, 'Finish', and 'Cancel' buttons."
Verify that the system provides a summary of the order and allows the user to complete or cancel the purchase.,Verify that the order summary and action buttons are visible and accessible in Safari browser,"• User is authenticated with valid credentials in Safari browser
• User has added at least one product to the cart
• User has completed the required checkout information and is on the order summary page","1. Open the Swag Labs web application in Safari browser.
2. Log in with valid credentials.
3. Add at least one product to the cart from the Inventory Page.
4. Click the cart icon to go to the Cart Page.
5. Click the 'Checkout' button.
6. Enter valid checkout information and click 'Continue'.
7. Observe the order summary, 'Finish', and 'Cancel' buttons."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that user's session is terminated and user is redirected to the Login Page after logging out via the logout option in Chrome,"• User is authenticated in the Swag Labs web application using Chrome.
• Logout option is present in the UI.","1. Authenticate as a user in the Swag Labs web application using Chrome.
2. Locate the logout option in the user interface.
3. Click the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that user's session is terminated and user is redirected to the Login Page after logging out via the logout option in Firefox,"• User is authenticated in the Swag Labs web application using Firefox.
• Logout option is present in the UI.","1. Authenticate as a user in the Swag Labs web application using Firefox.
2. Locate the logout option in the user interface.
3. Click the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that user's session is terminated and user is redirected to the Login Page after logging out via the logout option in Edge,"• User is authenticated in the Swag Labs web application using Edge.
• Logout option is present in the UI.","1. Authenticate as a user in the Swag Labs web application using Edge.
2. Locate the logout option in the user interface.
3. Click the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that user's session is terminated and user is redirected to the Login Page after logging out via the logout option in Safari,"• User is authenticated in the Swag Labs web application using Safari.
• Logout option is present in the UI.","1. Authenticate as a user in the Swag Labs web application using Safari.
2. Locate the logout option in the user interface.
3. Click the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that the logout option is visible and accessible to authenticated users in Chrome,User is authenticated in the Swag Labs web application using Chrome.,"1. Authenticate as a user in the Swag Labs web application using Chrome.
2. Observe the user interface for the presence of the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that the logout option is visible and accessible to authenticated users in Firefox,User is authenticated in the Swag Labs web application using Firefox.,"1. Authenticate as a user in the Swag Labs web application using Firefox.
2. Observe the user interface for the presence of the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that the logout option is visible and accessible to authenticated users in Edge,User is authenticated in the Swag Labs web application using Edge.,"1. Authenticate as a user in the Swag Labs web application using Edge.
2. Observe the user interface for the presence of the logout option."
Verify that authenticated users can log out if the option is provided in the UI.,Verify that the logout option is visible and accessible to authenticated users in Safari,User is authenticated in the Swag Labs web application using Safari.,"1. Authenticate as a user in the Swag Labs web application using Safari.
2. Observe the user interface for the presence of the logout option."
"Verify that non-authenticated users cannot access inventory, cart, or checkout pages.",Verify that non-authenticated users are redirected to the Login Page when accessing the Inventory Page,"• User is not authenticated in the Swag Labs web application.
• Inventory Page URL is accessible directly via browser address bar.","1. Open a supported browser (e.g., Chrome).
2. Navigate to the Inventory Page URL of the Swag Labs web application without logging in."
"Verify that non-authenticated users cannot access inventory, cart, or checkout pages.",Verify that non-authenticated users are redirected to the Login Page when accessing the Cart Page,"• User is not authenticated in the Swag Labs web application.
• Cart Page URL is accessible directly via browser address bar.","1. Open a supported browser (e.g., Firefox).
2. Navigate to the Cart Page URL of the Swag Labs web application without logging in."
"Verify that non-authenticated users cannot access inventory, cart, or checkout pages.",Verify that non-authenticated users are redirected to the Login Page when accessing the Checkout process,"• User is not authenticated in the Swag Labs web application.
• Checkout process URL is accessible directly via browser address bar.","1. Open a supported browser (e.g., Edge).
2. Navigate to the Checkout process URL of the Swag Labs web application without logging in."
"Verify that non-authenticated users cannot access inventory, cart, or checkout pages.",Verify that non-authenticated users are redirected to the Login Page when accessing protected pages in Safari browser,"• User is not authenticated in the Swag Labs web application.
• Safari browser is installed and accessible.","1. Open Safari browser.
2. Navigate to the Inventory Page URL without logging in.
3. Navigate to the Cart Page URL without logging in.
4. Navigate to the Checkout process URL without logging in."
Verify that each page loads within 2 seconds for all users except performance-glitch users.,Verify that the Login Page loads and becomes interactive within 2 seconds for a non-performance-glitch user,User is not using a performance-glitch user account.,"1. Open a modern browser (Chrome, Firefox, Edge, Safari).
2. Navigate to the Swag Labs Login Page using a non-performance-glitch user account.
3. Initiate the page load (e.g., by entering the URL or clicking a navigation link).
4. Measure the time from navigation initiation to when all visible UI elements and interactive controls are rendered and usable."
Verify that each page loads within 2 seconds for all users except performance-glitch users.,Verify that the Inventory Page loads and becomes interactive within 2 seconds for a non-performance-glitch user,User is authenticated and is not using a performance-glitch user account.,"1. Open a modern browser (Chrome, Firefox, Edge, Safari).
2. Log in to the Swag Labs application using a non-performance-glitch user account.
3. Navigate to the Inventory Page by clicking the appropriate navigation link or button.
4. Measure the time from navigation initiation to when all visible UI elements and interactive controls are rendered and usable."
Verify that each page loads within 2 seconds for all users except performance-glitch users.,Verify that the Cart Page loads and becomes interactive within 2 seconds for a non-performance-glitch user,User is authenticated and is not using a performance-glitch user account.,"1. Open a modern browser (Chrome, Firefox, Edge, Safari).
2. Log in to the Swag Labs application using a non-performance-glitch user account.
3. Navigate to the Cart Page by clicking the cart icon or link.
4. Measure the time from navigation initiation to when all visible UI elements and interactive controls are rendered and usable."
Verify that each page loads within 2 seconds for all users except performance-glitch users.,Verify that the Checkout Page loads and becomes interactive within 2 seconds for a non-performance-glitch user,User is authenticated and is not using a performance-glitch user account.,"1. Open a modern browser (Chrome, Firefox, Edge, Safari).
2. Log in to the Swag Labs application using a non-performance-glitch user account.
3. Navigate to the Checkout Page by clicking the appropriate navigation link or button from the Cart Page.
4. Measure the time from navigation initiation to when all visible UI elements and interactive controls are rendered and usable."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the 'Login' button on the Login Page is accessible and visible via keyboard navigation,User is on the Login Page of the Swag Labs web application.,"1. Navigate to the Login Page of the Swag Labs web application.
2. Use the Tab key to move focus to the 'Login' button.
3. Observe the visual focus indicator on the 'Login' button.
4. Press Enter or Space to activate the 'Login' button."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that 'Add to cart' buttons on the Inventory Page are accessible and visible via keyboard navigation,User is on the Inventory Page of the Swag Labs web application.,"1. Navigate to the Inventory Page of the Swag Labs web application.
2. Use the Tab key to move focus to each 'Add to cart' button.
3. Observe the visual focus indicator on each 'Add to cart' button.
4. Press Enter or Space to activate an 'Add to cart' button."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the cart icon in the navigation bar is accessible and visible via keyboard navigation,User is on a page of the Swag Labs web application where the cart icon is present in the navigation bar.,"1. Navigate to any page where the cart icon is present in the navigation bar.
2. Use the Tab key to move focus to the cart icon.
3. Observe the visual focus indicator on the cart icon.
4. Press Enter or Space to activate the cart icon."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the 'Checkout' button on the Cart Page is accessible and visible via keyboard navigation,User is on the Cart Page of the Swag Labs web application.,"1. Navigate to the Cart Page of the Swag Labs web application.
2. Use the Tab key to move focus to the 'Checkout' button.
3. Observe the visual focus indicator on the 'Checkout' button.
4. Press Enter or Space to activate the 'Checkout' button."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the 'Continue' button on the Checkout Page is accessible and visible via keyboard navigation,User is on the Checkout Page of the Swag Labs web application.,"1. Navigate to the Checkout Page of the Swag Labs web application.
2. Use the Tab key to move focus to the 'Continue' button.
3. Observe the visual focus indicator on the 'Continue' button.
4. Press Enter or Space to activate the 'Continue' button."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the 'Cancel' button on the Checkout Page is accessible and visible via keyboard navigation,User is on the Checkout Page of the Swag Labs web application.,"1. Navigate to the Checkout Page of the Swag Labs web application.
2. Use the Tab key to move focus to the 'Cancel' button.
3. Observe the visual focus indicator on the 'Cancel' button.
4. Press Enter or Space to activate the 'Cancel' button."
"Verify that all interactive elements (login, cart, checkout) are accessible via keyboard and visible on the UI.",Verify that the 'Finish' button on the Checkout Page is accessible and visible via keyboard navigation,User is on the Checkout Page of the Swag Labs web application.,"1. Navigate to the Checkout Page of the Swag Labs web application.
2. Use the Tab key to move focus to the 'Finish' button.
3. Observe the visual focus indicator on the 'Finish' button.
4. Press Enter or Space to activate the 'Finish' button."
Verify that the password field masks user credentials during input.,Verify that each character entered into the password field is immediately masked with a symbol on the Login Page,"• User is on the Login Page of the Swag Labs web application
• The password input field is present and visible","1. Navigate to the Login Page of the Swag Labs web application.
2. Locate the password input field.
3. Enter the character 'a' into the password field using the keyboard.
4. Observe the password field display after entering the character."
Verify that the password field masks user credentials during input.,Verify that all characters entered into the password field are masked as symbols for the entire duration they are displayed,"• User is on the Login Page of the Swag Labs web application
• The password input field is present and visible","1. Navigate to the Login Page of the Swag Labs web application.
2. Locate the password input field.
3. Enter the string 'Password123!' into the password field using the keyboard.
4. Observe the password field display after entering the entire string."
Verify that the password field masks user credentials during input.,Verify that characters pasted into the password field are immediately masked as symbols,"• User is on the Login Page of the Swag Labs web application
• The password input field is present and visible","1. Navigate to the Login Page of the Swag Labs web application.
2. Locate the password input field.
3. Copy the string 'SecretPass' to the clipboard.
4. Paste the string into the password field.
5. Observe the password field display after pasting."
Verify that the password field masks user credentials during input.,Verify that password masking cannot be bypassed by standard user interactions,"• User is on the Login Page of the Swag Labs web application
• The password input field is present and visible","1. Navigate to the Login Page of the Swag Labs web application.
2. Locate the password input field.
3. Enter the string 'BypassTest' into the password field using the keyboard.
4. Attempt to reveal the entered password using standard browser features (e.g., right-click, inspect element, copy-paste from the field, drag-and-drop, or toggling input type via UI if available).
5. Observe the password field display and any UI changes."
Verify that the password field masks user credentials during input.,"Verify that password masking is consistent across all supported browsers (Chrome, Firefox, Edge, Safari)","• User is on the Login Page of the Swag Labs web application in each supported browser
• The password input field is present and visible in each browser","1. Open the Login Page of the Swag Labs web application in Chrome.
2. Enter the string 'BrowserTest' into the password field.
3. Observe the password field display.
4. Repeat steps 1-3 in Firefox.
5. Repeat steps 1-3 in Edge.
6. Repeat steps 1-3 in Safari."
Verify that error messages do not reveal sensitive system information.,Verify that login error message does not reveal sensitive system information when invalid credentials are entered,"• The Swag Labs web application is accessible in a supported browser.
• The Login Page is loaded and visible to the user.","1. Navigate to the Login Page of the Swag Labs web application.
2. Enter an invalid username in the Username input field.
3. Enter an invalid password in the Password input field.
4. Click the 'Login' button."
Verify that error messages do not reveal sensitive system information.,Verify that login error message does not reveal sensitive system information when a locked account is used,"• The Swag Labs web application is accessible in a supported browser.
• The Login Page is loaded and visible to the user.
• The test account is locked in the system.","1. Navigate to the Login Page of the Swag Labs web application.
2. Enter the username of a locked account in the Username input field.
3. Enter the correct password for the locked account in the Password input field.
4. Click the 'Login' button."
Verify that error messages do not reveal sensitive system information.,Verify that form validation error messages do not reveal sensitive system information when required fields are left blank,"• The Swag Labs web application is accessible in a supported browser.
• The form page is loaded and visible to the user.","1. Navigate to a form page in the Swag Labs web application (e.g., registration or checkout).
2. Leave one or more required fields blank.
3. Submit the form."
Verify that error messages do not reveal sensitive system information.,Verify that system error messages do not reveal sensitive system information when a system malfunction occurs,"• The Swag Labs web application is accessible in a supported browser.
• The user is performing an action that can trigger a system malfunction (e.g., submitting a form or loading a page).","1. Trigger a known system malfunction (e.g., disconnect the network or simulate a server error) while using the Swag Labs web application.
2. Observe the error message displayed to the user."
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the Login Page displays the Username input field upon loading,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).",1. Navigate to the Login Page of the Swag Labs web application.
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the Login Page displays the Password input field with masked input upon loading,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).","1. Navigate to the Login Page of the Swag Labs web application.
2. Enter any characters into the Password input field using the keyboard."
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the Login Page displays the 'Login' button upon loading,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).",1. Navigate to the Login Page of the Swag Labs web application.
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the error message area is not visible on the Login Page upon initial load,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).",1. Navigate to the Login Page of the Swag Labs web application.
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the error message area appears and displays a generic error message when login fails due to invalid credentials,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).","1. Navigate to the Login Page of the Swag Labs web application.
2. Enter an invalid username in the Username input field.
3. Enter an invalid password in the Password input field.
4. Click the 'Login' button."
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the error message area appears and displays a generic error message when login fails due to a locked account,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).
• The account used is locked in the system.","1. Navigate to the Login Page of the Swag Labs web application.
2. Enter a valid username for a locked account in the Username input field.
3. Enter the correct password for the locked account in the Password input field.
4. Click the 'Login' button."
"Verify that the Login Page contains username and password input fields, a 'Login' button, and displays error messages on failed login.",Verify that the Password input field masks input when pasting text,"• User is not authenticated.
• User accesses the application via a supported browser (Chrome, Firefox, Edge, Safari).","1. Navigate to the Login Page of the Swag Labs web application.
2. Copy any text to the clipboard.
3. Paste the text into the Password input field."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the Inventory Page displays a product catalog with each product's name,"• User is authenticated and has access to the Inventory Page via a supported browser.
• At least one product is available in the product catalog.","1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the product catalog section.
4. For each product entry, check for the presence of the product's name."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the Inventory Page displays a product catalog with each product's image,"• User is authenticated and has access to the Inventory Page via a supported browser.
• At least one product is available in the product catalog.","1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the product catalog section.
4. For each product entry, check for the presence of an image representing the product."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the Inventory Page displays a product catalog with each product's price,"• User is authenticated and has access to the Inventory Page via a supported browser.
• At least one product is available in the product catalog.","1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the product catalog section.
4. For each product entry, check for the presence of the product's price."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the Inventory Page displays an 'Add to cart' button for each product,"• User is authenticated and has access to the Inventory Page via a supported browser.
• At least one product is available in the product catalog.","1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the product catalog section.
4. For each product entry, check for the presence of an 'Add to cart' button."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the Inventory Page displays a navigation bar with a cart icon,User is authenticated and has access to the Inventory Page via a supported browser.,"1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the navigation bar.
4. Check for the presence of the cart icon in the navigation bar."
"Verify that the Inventory Page displays a product catalog with name, image, price, 'Add to cart' buttons, and a navigation bar with cart icon.",Verify that the cart icon displays the current cart item count on the Inventory Page,"• User is authenticated and has access to the Inventory Page via a supported browser.
• The user's cart contains zero or more items.","1. Log in with valid credentials.
2. Observe the Inventory Page after successful login.
3. Locate the cart icon in the navigation bar.
4. Check the number displayed on the cart icon reflects the current number of items in the user's cart."
"Verify that the Cart Page lists added products, provides an option to remove products, and includes a 'Checkout' button.",Verify that the Cart Page displays a list of all added products with clear identification,"• The user is authenticated with valid credentials.
• At least two products have been added to the cart from the Inventory Page.","1. Log in to the Swag Labs web application with valid credentials.
2. Add two distinct products to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to navigate to the Cart Page."
"Verify that the Cart Page lists added products, provides an option to remove products, and includes a 'Checkout' button.",Verify that each product in the Cart Page list includes a remove option,"• The user is authenticated with valid credentials.
• At least one product has been added to the cart from the Inventory Page.","1. Log in to the Swag Labs web application with valid credentials.
2. Add a product to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to navigate to the Cart Page.
4. Observe the product entry in the cart list."
"Verify that the Cart Page lists added products, provides an option to remove products, and includes a 'Checkout' button.",Verify that the remove option removes the product from the Cart Page list and updates the cart icon,"• The user is authenticated with valid credentials.
• At least two products have been added to the cart from the Inventory Page.","1. Log in to the Swag Labs web application with valid credentials.
2. Add two products to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to navigate to the Cart Page.
4. Click the remove option for one of the products in the cart list."
"Verify that the Cart Page lists added products, provides an option to remove products, and includes a 'Checkout' button.",Verify that the 'Checkout' button is present and accessible on the Cart Page,"• The user is authenticated with valid credentials.
• At least one product has been added to the cart from the Inventory Page.","1. Log in to the Swag Labs web application with valid credentials.
2. Add a product to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to navigate to the Cart Page."
"Verify that the Cart Page lists added products, provides an option to remove products, and includes a 'Checkout' button.","Verify that all Cart Page elements (product list, remove option, 'Checkout' button) are visible and accessible upon loading","• The user is authenticated with valid credentials.
• At least one product has been added to the cart from the Inventory Page.","1. Log in to the Swag Labs web application with valid credentials.
2. Add a product to the cart from the Inventory Page.
3. Click the cart icon in the navigation bar to navigate to the Cart Page."
"Verify that the Checkout screen includes input for user information (first name, last name, ZIP/postal code), 'Continue' and 'Cancel' buttons, an overview, and a 'Finish' button.","Verify that the Checkout screen displays input fields for first name, last name, and ZIP/postal code","• User is authenticated
• User has at least one product in the cart
• User is on the Cart Page","1. Click the 'Checkout' button on the Cart Page.
2. Observe the Checkout screen for input fields labeled 'First Name', 'Last Name', and 'ZIP/Postal Code'."
"Verify that the Checkout screen includes input for user information (first name, last name, ZIP/postal code), 'Continue' and 'Cancel' buttons, an overview, and a 'Finish' button.",Verify that the Checkout screen displays 'Continue' and 'Cancel' buttons,"• User is authenticated
• User has at least one product in the cart
• User is on the Cart Page","1. Click the 'Checkout' button on the Cart Page.
2. Observe the Checkout screen for the presence of 'Continue' and 'Cancel' buttons."
"Verify that the Checkout screen includes input for user information (first name, last name, ZIP/postal code), 'Continue' and 'Cancel' buttons, an overview, and a 'Finish' button.","Verify that after entering required information and clicking 'Continue', the order overview and 'Finish' button are displayed","• User is authenticated
• User has at least one product in the cart
• User is on the Cart Page","1. Click the 'Checkout' button on the Cart Page.
2. Enter a valid first name in the 'First Name' input field.
3. Enter a valid last name in the 'Last Name' input field.
4. Enter a valid ZIP/postal code in the 'ZIP/Postal Code' input field.
5. Click the 'Continue' button.
6. Observe the next screen for the order overview and 'Finish' button."
"Verify that the Checkout screen includes input for user information (first name, last name, ZIP/postal code), 'Continue' and 'Cancel' buttons, an overview, and a 'Finish' button.","Verify that all Checkout elements (input fields, 'Continue', 'Cancel', order overview, and 'Finish' button) are visible and accessible","• User is authenticated
• User has at least one product in the cart
• User is on the Cart Page","1. Click the 'Checkout' button on the Cart Page.
2. Observe the Checkout screen for the visibility and accessibility of 'First Name', 'Last Name', 'ZIP/Postal Code' input fields, 'Continue' and 'Cancel' buttons.
3. Enter valid information in all input fields.
4. Click the 'Continue' button.
5. Observe the order overview screen for the visibility and accessibility of the order overview and 'Finish' button."
